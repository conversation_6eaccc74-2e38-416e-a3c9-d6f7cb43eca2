# 学习管理系统配置示例
# 复制此文件为 .env 并填入实际值

# 基础配置
STUDY_USERNAME=your_username
STUDY_PASSWORD=your_password

# Notion配置
NOTION_TOKEN=your_notion_integration_token
DATABASE_ID=your_notion_database_id
# 注意：总览页面ID将从每日记录中的"总览"列动态获取

# 图片识别AI配置（用于描述图片内容）
VISION_API_KEY=your_vision_api_key
VISION_BASE_URL=https://api.openai.com/v1
VISION_MODEL=gpt-4o-mini

# 文字处理AI配置（用于分析和处理文字内容）
TEXT_API_KEY=your_text_api_key
TEXT_BASE_URL=https://api.openai.com/v1
TEXT_MODEL=gpt-4o

# 文件上传配置
UPLOAD_FOLDER=./uploads
MAX_CONTENT_LENGTH=16777216

# 使用说明：
# 1. 将此文件复制为 .env
# 2. 填入实际的配置值
# 3. 可以为图片识别和文字处理使用不同的API密钥和模型
# 4. 推荐图片识别使用轻量模型，文字处理使用强模型
