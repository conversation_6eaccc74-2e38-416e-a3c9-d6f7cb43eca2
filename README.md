# 学习管理系统 - 升级版

一个基于AI的智能学习管理系统，支持图片识别和学习内容分析。

## 🆕 新功能特性

### 🎨 前端优化
- **现代化UI设计**：采用毛玻璃效果、渐变背景和动画效果
- **响应式布局**：完美适配桌面和移动设备
- **交互动画**：按钮悬停效果、图标动画、页面过渡动画
- **视觉反馈**：加载状态、进度条、状态提示

### 🤖 AI架构重构
- **双AI模型架构**：
  - **图片识别AI**：专门用于描述图片内容
  - **文字处理AI**：专门用于分析和处理文字内容
- **两阶段处理流程**：
  1. 图片描述：AI详细描述图片中的学习内容
  2. 智能分析：基于描述提取学习信息和知识点

### 📊 增强的分析结果
- **图片内容识别**：显示AI对图片的详细描述
- **学习分析**：提取科目和具体学习内容
- **分析详情**：包含置信度和分析时间
- **智能总览集成**：自动从每日记录的"总览"列获取页面ID，将图片添加到对应的总览页面

## 🚀 快速开始

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 配置环境变量
```bash
# 基础配置
export STUDY_USERNAME="your_username"
export STUDY_PASSWORD="your_password"
export NOTION_TOKEN="your_notion_token"
export DATABASE_ID="your_database_id"
# 注意：总览页面ID会从每日记录的"总览"列自动获取

# 图片识别AI配置（用于描述图片内容）
export VISION_API_KEY="your_vision_api_key"
export VISION_BASE_URL="https://api.openai.com/v1"  # 或其他API地址
export VISION_MODEL="gpt-4o-mini"  # 或其他视觉模型

# 文字处理AI配置（用于分析和处理文字内容）
export TEXT_API_KEY="your_text_api_key"
export TEXT_BASE_URL="https://api.openai.com/v1"  # 或其他API地址
export TEXT_MODEL="gpt-4o"  # 推荐使用更强的模型
```

### 3. 启动服务
```bash
python main.py
```

### 4. 访问系统
打开浏览器访问：http://localhost:5001

## 🔧 配置说明

### Notion数据库结构
系统要求Notion数据库包含以下列：
- **日期**：Date类型，用于筛选今日记录
- **数学**、**英语**、**政治**、**计算机408**：Rich Text类型，存储学习内容
- **总结（AI生成）**：Rich Text类型，存储AI生成的总结
- **总览**：Relation类型或包含页面链接的Rich Text类型，指向每日的总览页面

### AI模型配置
系统支持分别配置两个AI模型：

1. **图片识别模型**（VISION_*）：
   - 负责识别和描述图片内容
   - 推荐使用支持视觉的模型如 gpt-4o-mini
   - 可以使用较轻量的模型以节省成本

2. **文字处理模型**（TEXT_*）：
   - 负责分析文字内容和生成总结
   - 推荐使用更强的模型如 gpt-4o
   - 处理复杂的文字分析和内容合并

### 环境变量详解
- `VISION_API_KEY`: 图片识别AI的API密钥
- `VISION_BASE_URL`: 图片识别AI的API地址
- `VISION_MODEL`: 图片识别使用的模型名称
- `TEXT_API_KEY`: 文字处理AI的API密钥
- `TEXT_BASE_URL`: 文字处理AI的API地址
- `TEXT_MODEL`: 文字处理使用的模型名称

## 📱 功能介绍

### 📸 图片分析
1. 拖拽或点击上传学习图片
2. AI自动识别图片内容
3. 显示详细的图片描述
4. 提取学习科目和知识点
5. 自动更新到Notion数据库

### ✏️ 手动录入
- 选择学习科目
- 输入学习内容
- 支持与现有内容智能合并
- 一键更新到Notion

### 📊 数据查看
- 查看当日所有学习记录
- 按科目分类显示
- 实时数据同步

### 🤖 AI总结
- 基于当日学习内容生成智能总结
- 简洁有条理的复盘内容
- 自动保存到Notion

## 🎯 使用建议

1. **模型选择**：
   - 图片识别可使用 gpt-4o-mini 等轻量模型
   - 文字处理建议使用 gpt-4o 等强模型

2. **成本优化**：
   - 可以使用不同的API提供商
   - 根据需求调整模型配置

3. **数据安全**：
   - 所有配置通过环境变量管理
   - 支持本地部署

## 🔄 更新日志

### v2.0.0
- 重构AI调用架构，支持双模型配置
- 全新的现代化UI设计
- 增强的图片分析功能
- 改进的用户体验和交互动画

