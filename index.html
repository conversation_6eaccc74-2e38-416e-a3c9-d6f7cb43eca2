<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>学习管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            background-attachment: fixed;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 1rem;
            position: relative;
            overflow-x: hidden;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.05)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.05)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.03)"/><circle cx="20" cy="80" r="0.5" fill="rgba(255,255,255,0.03)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            pointer-events: none;
            z-index: -1;
        }

        .container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            box-shadow:
                0 32px 64px rgba(0,0,0,0.12),
                0 16px 32px rgba(0,0,0,0.08),
                inset 0 1px 0 rgba(255,255,255,0.8);
            border: 1px solid rgba(255,255,255,0.2);
            overflow: hidden;
            width: 100%;
            max-width: 1000px;
            min-height: 600px;
            position: relative;
            animation: containerFadeIn 0.8s ease-out;
        }

        @keyframes containerFadeIn {
            from {
                opacity: 0;
                transform: translateY(30px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 2rem;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: headerGlow 6s ease-in-out infinite alternate;
        }

        @keyframes headerGlow {
            0% { transform: translate(-10px, -10px) scale(1); }
            100% { transform: translate(10px, 10px) scale(1.05); }
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            position: relative;
            z-index: 1;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
            animation: titleFloat 3s ease-in-out infinite alternate;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
            position: relative;
            z-index: 1;
            text-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }

        @keyframes titleFloat {
            0% { transform: translateY(0px); }
            100% { transform: translateY(-3px); }
        }

        .logout-btn {
            position: absolute;
            top: 1rem;
            right: 1rem;
            background: rgba(255,255,255,0.2);
            color: white;
            border: 1px solid rgba(255,255,255,0.3);
            padding: 0.5rem 1rem;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        .logout-btn:hover {
            background: rgba(255,255,255,0.3);
        }

        .login-form, .main-content {
            padding: 2rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #333;
        }

        .form-group input, .form-group select, .form-group textarea {
            width: 100%;
            padding: 1rem;
            border: 2px solid #e1e1e1;
            border-radius: 10px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-group input:focus, .form-group select:focus, .form-group textarea:focus {
            outline: none;
            border-color: #4facfe;
            box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 12px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            text-transform: uppercase;
            letter-spacing: 1px;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            position: relative;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn:hover {
            transform: translateY(-3px) scale(1.02);
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
        }

        .btn:active {
            transform: translateY(-1px) scale(0.98);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .btn:disabled::before {
            display: none;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            color: #333;
        }

        .btn-success {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            color: #333;
        }

        .tabs {
            display: flex;
            background: #f8f9fa;
            border-radius: 10px;
            padding: 0.5rem;
            margin-bottom: 2rem;
            gap: 0.5rem;
        }

        .tab {
            flex: 1;
            padding: 1rem;
            text-align: center;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 600;
        }

        .tab.active {
            background: white;
            color: #4facfe;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .tab-content {
            display: none;
            animation: fadeIn 0.3s ease;
        }

        .tab-content.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .upload-area {
            border: 2px dashed #4facfe;
            border-radius: 20px;
            padding: 3rem;
            text-align: center;
            background:
                radial-gradient(circle at 30% 30%, rgba(79, 172, 254, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 70% 70%, rgba(0, 242, 254, 0.1) 0%, transparent 50%),
                rgba(79, 172, 254, 0.05);
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            cursor: pointer;
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(10px);
        }

        .upload-area::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, #4facfe, #00f2fe, #4facfe);
            border-radius: 20px;
            z-index: -1;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .upload-area:hover {
            background:
                radial-gradient(circle at 30% 30%, rgba(79, 172, 254, 0.15) 0%, transparent 50%),
                radial-gradient(circle at 70% 70%, rgba(0, 242, 254, 0.15) 0%, transparent 50%),
                rgba(79, 172, 254, 0.1);
            transform: translateY(-5px) scale(1.02);
            box-shadow: 0 20px 40px rgba(79, 172, 254, 0.2);
        }

        .upload-area:hover::before {
            opacity: 0.1;
        }

        .upload-area.dragover {
            background:
                radial-gradient(circle at 50% 50%, rgba(79, 172, 254, 0.3) 0%, transparent 70%),
                rgba(79, 172, 254, 0.2);
            border-color: #667eea;
            transform: scale(1.05);
            box-shadow: 0 25px 50px rgba(79, 172, 254, 0.3);
        }

        .upload-area.dragover::before {
            opacity: 0.2;
        }

        .upload-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            animation: iconBounce 2s ease-in-out infinite;
            display: inline-block;
        }

        @keyframes iconBounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }

        .upload-area:hover .upload-icon {
            animation: iconSpin 0.6s ease-in-out;
        }

        @keyframes iconSpin {
            0% { transform: rotate(0deg) scale(1); }
            50% { transform: rotate(180deg) scale(1.1); }
            100% { transform: rotate(360deg) scale(1); }
        }

        .progress-container {
            margin: 1rem 0;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e1e1e1;
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4facfe, #00f2fe);
            transition: width 0.3s ease;
            width: 0%;
        }

        .status {
            padding: 1rem;
            border-radius: 10px;
            margin: 1rem 0;
            font-weight: 600;
            display: none;
        }

        .status.show {
            display: block;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .image-preview {
            max-width: 100%;
            max-height: 300px;
            border-radius: 10px;
            margin: 1rem 0;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            display: none;
        }

        .subject-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin: 1rem 0;
        }

        .subject-card {
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 15px;
            border: 2px solid transparent;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .subject-card:hover {
            border-color: #4facfe;
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }

        .subject-card.selected {
            border-color: #4facfe;
            background: rgba(79, 172, 254, 0.1);
        }

        .subject-card h3 {
            color: #333;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .subject-card p {
            color: #666;
            font-size: 0.9rem;
        }

        .data-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 1.5rem;
            border-radius: 16px;
            margin-bottom: 1rem;
            border-left: 4px solid #4facfe;
            box-shadow: 0 4px 12px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .data-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 2px;
            background: linear-gradient(90deg, #4facfe, #00f2fe);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .data-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(79, 172, 254, 0.15);
        }

        .data-card:hover::before {
            transform: scaleX(1);
        }

        .data-card h4 {
            color: #333;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-weight: 600;
        }

        .data-card .content {
            color: #555;
            line-height: 1.7;
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            padding: 1.2rem;
            border-radius: 12px;
            margin-top: 0.5rem;
            border: 1px solid rgba(79, 172, 254, 0.1);
            transition: all 0.3s ease;
        }

        .data-card .content:hover {
            background: rgba(255, 255, 255, 0.95);
            border-color: rgba(79, 172, 254, 0.2);
        }

        .hidden {
            display: none !important;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #ffffff;
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        @media (max-width: 768px) {
            .container {
                width: 100%;
                margin: 0;
                min-height: 100vh;
                border-radius: 0;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .tabs {
                flex-direction: column;
            }
            
            .subject-grid {
                grid-template-columns: 1fr;
            }

            .upload-area {
                padding: 2rem 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 登录界面 -->
        <div id="loginPage">
            <div class="header">
                <h1>🎓 学习管理系统</h1>
                <p>智能化学习进度追踪与分析</p>
            </div>
            <div class="login-form">
                <form id="loginForm">
                    <div class="form-group">
                        <label for="username">用户名</label>
                        <input type="text" id="username" name="username" required placeholder="请输入用户名" autocomplete="username">
                    </div>
                    <div class="form-group">
                        <label for="password">密码</label>
                        <input type="password" id="password" name="password" required placeholder="请输入密码" autocomplete="current-password">
                    </div>
                    <button type="submit" class="btn" style="width: 100%;">
                        <span id="loginBtnText">🔑 登录</span>
                        <div class="loading hidden" id="loginLoading"></div>
                    </button>
                </form>
                <div id="loginStatus" class="status"></div>
            </div>
        </div>

        <!-- 主界面 -->
        <div id="mainPage" class="hidden">
            <div class="header" style="position: relative;">
                <h1>🎓 学习管理系统</h1>
                <p id="dateDisplay"></p>
                <button class="logout-btn" onclick="logout()">🚪 退出登录</button>
            </div>

            <div class="main-content">
                <div class="tabs">
                    <div class="tab active" onclick="showTab('upload')">📸 图片分析</div>
                    <div class="tab" onclick="showTab('manual')">✏️ 手动录入</div>
                    <div class="tab" onclick="showTab('view')">📊 数据查看</div>
                    <div class="tab" onclick="showTab('summary')">🤖 AI总结</div>
                </div>

                <!-- 图片上传分析 -->
                <div id="uploadTab" class="tab-content active">
                    <div class="upload-area" onclick="document.getElementById('imageInput').click()" 
                         ondragover="handleDragOver(event)" ondrop="handleDrop(event)">
                        <div class="upload-icon">📷</div>
                        <h3>拖拽或点击上传学习图片</h3>
                        <p>支持 JPG、PNG 等格式，AI将自动识别学习内容</p>
                        <input type="file" id="imageInput" accept="image/*" style="display: none;" onchange="handleFileSelect(event)">
                    </div>
                    
                    <div class="progress-container hidden" id="uploadProgressContainer">
                        <div class="progress-bar">
                            <div class="progress-fill" id="uploadProgressFill"></div>
                        </div>
                        <p style="text-align: center; margin-top: 0.5rem;">上传中...</p>
                    </div>
                    
                    <img id="imagePreview" class="image-preview">
                    <div id="uploadStatus" class="status"></div>
                    
                    <div id="analysisResult" class="hidden" style="margin-top: 1rem;">
                        <div class="data-card">
                            <h4>🔍 图片内容识别</h4>
                            <div class="content" id="imageDescription"></div>
                        </div>
                        <div class="data-card">
                            <h4>🤖 AI 学习分析</h4>
                            <div class="content" id="analysisContent"></div>
                        </div>
                        <div class="data-card">
                            <h4>📊 分析详情</h4>
                            <div class="content" id="analysisDetails"></div>
                        </div>
                    </div>
                </div>

                <!-- 手动输入 -->
                <div id="manualTab" class="tab-content">
                    <div class="form-group">
                        <label>选择科目</label>
                        <div class="subject-grid">
                            <div class="subject-card" onclick="selectSubject('数学')" data-subject="数学">
                                <h3><span>📐</span>数学</h3>
                                <p>高等数学、线性代数、概率统计</p>
                            </div>
                            <div class="subject-card" onclick="selectSubject('英语')" data-subject="英语">
                                <h3><span>📚</span>英语</h3>
                                <p>词汇、阅读、写作、翻译</p>
                            </div>
                            <div class="subject-card" onclick="selectSubject('政治')" data-subject="政治">
                                <h3><span>🏛️</span>政治</h3>
                                <p>马克思主义、毛概、思修</p>
                            </div>
                            <div class="subject-card" onclick="selectSubject('计算机408')" data-subject="计算机408">
                                <h3><span>💻</span>计算机408</h3>
                                <p>数据结构、算法、操作系统</p>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="studyContent">学习内容</label>
                        <textarea id="studyContent" placeholder="请输入今天的学习内容..." rows="6"></textarea>
                    </div>

                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="mergeContent" style="width: auto; margin-right: 0.5rem;">
                            与现有内容智能合并
                        </label>
                    </div>

                    <button type="button" class="btn" onclick="updateStudyRecord()">
                        <span id="updateBtnText">💾 更新记录</span>
                        <div class="loading hidden" id="updateLoading"></div>
                    </button>
                    <div id="updateStatus" class="status"></div>
                </div>

                <!-- 数据查看 -->
                <div id="viewTab" class="tab-content">
                    <button type="button" class="btn btn-secondary" onclick="loadTodayData()">
                        <span id="loadBtnText">🔄 刷新数据</span>
                        <div class="loading hidden" id="loadLoading"></div>
                    </button>
                    <div id="loadStatus" class="status"></div>
                    <div id="todayData"></div>
                </div>

                <!-- AI总结 -->
                <div id="summaryTab" class="tab-content">
                    <button type="button" class="btn btn-success" onclick="generateSummary()">
                        <span id="summaryBtnText">🤖 生成AI总结</span>
                        <div class="loading hidden" id="summaryLoading"></div>
                    </button>
                    <div id="summaryStatus" class="status"></div>
                    <div id="summaryResult"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let selectedSubject = '';
        let isLoggedIn = false;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateDateDisplay();

            // 登录表单提交
            document.getElementById('loginForm').addEventListener('submit', function(e) {
                e.preventDefault();
                login();
            });
        });

        // 更新日期显示
        function updateDateDisplay() {
            const now = new Date();
            const dateStr = now.toLocaleDateString('zh-CN', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                weekday: 'long'
            });
            const timeStr = now.toLocaleTimeString('zh-CN');

            const dateDisplay = document.getElementById('dateDisplay');
            if (dateDisplay) {
                dateDisplay.textContent = `${dateStr} ${timeStr}`;
            }
        }

        // 登录功能
        async function login() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const loginBtn = document.getElementById('loginBtnText');
            const loginLoading = document.getElementById('loginLoading');
            const loginStatus = document.getElementById('loginStatus');

            // 显示加载状态
            loginBtn.style.display = 'none';
            loginLoading.classList.remove('hidden');
            loginStatus.className = 'status';

            try {
                const response = await fetch('/api/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ username, password })
                });

                const result = await response.json();

                if (result.success) {
                    isLoggedIn = true;
                    showStatus(loginStatus, result.message, 'success');

                    // 延迟显示主界面
                    setTimeout(() => {
                        document.getElementById('loginPage').classList.add('hidden');
                        document.getElementById('mainPage').classList.remove('hidden');
                        updateDateDisplay();
                        setInterval(updateDateDisplay, 1000); // 每秒更新时间
                    }, 1000);
                } else {
                    showStatus(loginStatus, result.message, 'error');
                }
            } catch (error) {
                showStatus(loginStatus, '登录失败，请检查网络连接', 'error');
            } finally {
                // 恢复按钮状态
                loginBtn.style.display = 'inline';
                loginLoading.classList.add('hidden');
            }
        }

        // 退出登录
        function logout() {
            isLoggedIn = false;
            document.getElementById('mainPage').classList.add('hidden');
            document.getElementById('loginPage').classList.remove('hidden');

            // 重置表单
            document.getElementById('loginForm').reset();
            document.getElementById('loginStatus').className = 'status';
        }

        // 显示状态信息
        function showStatus(element, message, type) {
            element.textContent = message;
            element.className = `status show ${type}`;

            // 3秒后自动隐藏成功消息
            if (type === 'success') {
                setTimeout(() => {
                    element.classList.remove('show');
                }, 3000);
            }
        }

        // 标签页切换
        function showTab(tabName) {
            // 隐藏所有标签页内容
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });

            // 移除所有标签的active类
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // 显示选中的标签页
            document.getElementById(tabName + 'Tab').classList.add('active');

            // 添加active类到对应标签
            event.target.classList.add('active');
        }

        // 文件拖拽处理
        function handleDragOver(e) {
            e.preventDefault();
            e.stopPropagation();
            document.querySelector('.upload-area').classList.add('dragover');
        }

        function handleDrop(e) {
            e.preventDefault();
            e.stopPropagation();
            document.querySelector('.upload-area').classList.remove('dragover');

            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFile(files[0]);
            }
        }

        // 文件选择处理
        function handleFileSelect(e) {
            const file = e.target.files[0];
            if (file) {
                handleFile(file);
            }
        }

        // 处理文件上传
        async function handleFile(file) {
            if (!file.type.startsWith('image/')) {
                showStatus(document.getElementById('uploadStatus'), '请选择图片文件', 'error');
                return;
            }

            // 显示图片预览
            const reader = new FileReader();
            reader.onload = function(e) {
                const preview = document.getElementById('imagePreview');
                preview.src = e.target.result;
                preview.style.display = 'block';
            };
            reader.readAsDataURL(file);

            // 显示上传进度
            const progressContainer = document.getElementById('uploadProgressContainer');
            const progressFill = document.getElementById('uploadProgressFill');
            const uploadStatus = document.getElementById('uploadStatus');

            progressContainer.classList.remove('hidden');
            uploadStatus.className = 'status';

            // 模拟上传进度
            let progress = 0;
            const progressInterval = setInterval(() => {
                progress += Math.random() * 30;
                if (progress > 90) progress = 90;
                progressFill.style.width = progress + '%';
            }, 200);

            try {
                const formData = new FormData();
                formData.append('image', file);

                const response = await fetch('/api/upload-image', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                // 完成进度条
                clearInterval(progressInterval);
                progressFill.style.width = '100%';

                if (result.success) {
                    showStatus(uploadStatus, result.message, 'success');
                    displayAnalysisResult(result.analysis);
                } else {
                    showStatus(uploadStatus, result.message, 'error');
                }
            } catch (error) {
                clearInterval(progressInterval);
                showStatus(uploadStatus, '上传失败，请检查网络连接', 'error');
            } finally {
                // 隐藏进度条
                setTimeout(() => {
                    progressContainer.classList.add('hidden');
                    progressFill.style.width = '0%';
                }, 1000);
            }
        }

        // 显示分析结果
        function displayAnalysisResult(analysis) {
            const resultDiv = document.getElementById('analysisResult');
            const imageDescDiv = document.getElementById('imageDescription');
            const analysisContentDiv = document.getElementById('analysisContent');
            const analysisDetailsDiv = document.getElementById('analysisDetails');

            // 显示图片描述
            imageDescDiv.textContent = analysis.image_description || '无法获取图片描述';

            // 显示学习分析
            analysisContentDiv.innerHTML = `
                <strong>科目：</strong>${analysis.subject}<br>
                <strong>内容：</strong>${analysis.content}
            `;

            // 显示分析详情
            analysisDetailsDiv.innerHTML = `
                <strong>置信度：</strong>${analysis.confidence}<br>
                <strong>分析时间：</strong>${new Date().toLocaleString('zh-CN')}
            `;

            resultDiv.classList.remove('hidden');
        }

        // 选择科目
        function selectSubject(subject) {
            selectedSubject = subject;

            // 移除所有选中状态
            document.querySelectorAll('.subject-card').forEach(card => {
                card.classList.remove('selected');
            });

            // 添加选中状态
            document.querySelector(`[data-subject="${subject}"]`).classList.add('selected');
        }

        // 更新学习记录
        async function updateStudyRecord() {
            if (!selectedSubject) {
                showStatus(document.getElementById('updateStatus'), '请先选择科目', 'error');
                return;
            }

            const content = document.getElementById('studyContent').value.trim();
            if (!content) {
                showStatus(document.getElementById('updateStatus'), '请输入学习内容', 'error');
                return;
            }

            const merge = document.getElementById('mergeContent').checked;
            const updateBtn = document.getElementById('updateBtnText');
            const updateLoading = document.getElementById('updateLoading');
            const updateStatus = document.getElementById('updateStatus');

            // 显示加载状态
            updateBtn.style.display = 'none';
            updateLoading.classList.remove('hidden');
            updateStatus.className = 'status';

            try {
                const response = await fetch('/api/update-study', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        subject: selectedSubject,
                        content: content,
                        merge: merge
                    })
                });

                const result = await response.json();

                if (result.success) {
                    showStatus(updateStatus, result.message, 'success');

                    // 如果内容被合并，显示最终内容
                    if (result.final_content && result.final_content !== content) {
                        document.getElementById('studyContent').value = result.final_content;
                    }

                    // 清空表单
                    setTimeout(() => {
                        document.getElementById('studyContent').value = '';
                        document.getElementById('mergeContent').checked = false;
                        selectedSubject = '';
                        document.querySelectorAll('.subject-card').forEach(card => {
                            card.classList.remove('selected');
                        });
                    }, 2000);
                } else {
                    showStatus(updateStatus, result.message, 'error');
                }
            } catch (error) {
                showStatus(updateStatus, '更新失败，请检查网络连接', 'error');
            } finally {
                // 恢复按钮状态
                updateBtn.style.display = 'inline';
                updateLoading.classList.add('hidden');
            }
        }

        // 加载今日数据
        async function loadTodayData() {
            const loadBtn = document.getElementById('loadBtnText');
            const loadLoading = document.getElementById('loadLoading');
            const loadStatus = document.getElementById('loadStatus');
            const todayDataDiv = document.getElementById('todayData');

            // 显示加载状态
            loadBtn.style.display = 'none';
            loadLoading.classList.remove('hidden');
            loadStatus.className = 'status';

            try {
                const response = await fetch('/api/today-data');
                const result = await response.json();

                if (result.success) {
                    showStatus(loadStatus, '数据加载成功', 'success');
                    displayTodayData(result.data, result.date);
                } else {
                    showStatus(loadStatus, result.message, 'info');
                    todayDataDiv.innerHTML = '';
                }
            } catch (error) {
                showStatus(loadStatus, '加载失败，请检查网络连接', 'error');
            } finally {
                // 恢复按钮状态
                loadBtn.style.display = 'inline';
                loadLoading.classList.add('hidden');
            }
        }

        // 显示今日数据
        function displayTodayData(data, date) {
            const todayDataDiv = document.getElementById('todayData');
            let html = `<h3>📅 ${date} 学习记录</h3>`;

            const subjects = ['数学', '英语', '政治', '计算机408'];
            subjects.forEach(subject => {
                const content = data[subject];
                if (content) {
                    html += `
                        <div class="data-card">
                            <h4>${getSubjectIcon(subject)} ${subject}</h4>
                            <div class="content">${content}</div>
                        </div>
                    `;
                }
            });

            if (html === `<h3>📅 ${date} 学习记录</h3>`) {
                html += '<p style="text-align: center; color: #666; margin: 2rem 0;">今天还没有学习记录</p>';
            }

            todayDataDiv.innerHTML = html;
        }

        // 获取科目图标
        function getSubjectIcon(subject) {
            const icons = {
                '数学': '📐',
                '英语': '📚',
                '政治': '🏛️',
                '计算机408': '💻'
            };
            return icons[subject] || '📖';
        }

        // 生成AI总结
        async function generateSummary() {
            const summaryBtn = document.getElementById('summaryBtnText');
            const summaryLoading = document.getElementById('summaryLoading');
            const summaryStatus = document.getElementById('summaryStatus');
            const summaryResult = document.getElementById('summaryResult');

            // 显示加载状态
            summaryBtn.style.display = 'none';
            summaryLoading.classList.remove('hidden');
            summaryStatus.className = 'status';

            try {
                const response = await fetch('/api/generate-summary', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                const result = await response.json();

                if (result.success) {
                    showStatus(summaryStatus, result.message, 'success');

                    summaryResult.innerHTML = `
                        <div class="data-card">
                            <h4>🤖 AI学习总结</h4>
                            <div class="content">${result.summary}</div>
                        </div>
                    `;
                } else {
                    showStatus(summaryStatus, result.message, 'error');
                    summaryResult.innerHTML = '';
                }
            } catch (error) {
                showStatus(summaryStatus, '生成失败，请检查网络连接', 'error');
            } finally {
                // 恢复按钮状态
                summaryBtn.style.display = 'inline';
                summaryLoading.classList.add('hidden');
            }
        }

        // 防止拖拽到页面其他地方
        document.addEventListener('dragover', function(e) {
            e.preventDefault();
        });

        document.addEventListener('drop', function(e) {
            e.preventDefault();
        });
    </script>
</body>
</html>